#!/usr/bin/env node

/**
 * Video Configuration Management Script
 * 
 * Usage:
 *   node scripts/manage-video-config.js list
 *   node scripts/manage-video-config.js reorder "ace-intro,sabo-power,luffy-dragon"
 *   node scripts/manage-video-config.js add --id="new-video" --title="New Video" --path="new.mp4"
 *   node scripts/manage-video-config.js disable "luffy-dragon"
 *   node scripts/manage-video-config.js enable "luffy-dragon"
 */

const fs = require('fs');
const path = require('path');

const CONFIG_PATH = path.join(__dirname, '../src/config/videoConfig.json');

function loadConfig() {
  try {
    const content = fs.readFileSync(CONFIG_PATH, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error('Error loading config:', error.message);
    process.exit(1);
  }
}

function saveConfig(config) {
  try {
    fs.writeFileSync(CONFIG_PATH, JSON.stringify(config, null, 2));
    console.log('✅ Configuration saved successfully');
  } catch (error) {
    console.error('Error saving config:', error.message);
    process.exit(1);
  }
}

function listVideos() {
  const config = loadConfig();
  console.log('\n📹 Current Video Configuration:\n');
  
  const sortedVideos = config.videos
    .sort((a, b) => a.index - b.index);
  
  sortedVideos.forEach(video => {
    const status = video.enabled ? '✅' : '❌';
    const source = video.path ? `📁 ${video.path}` : `🌐 ${video.url}`;
    console.log(`${status} [${video.index}] ${video.title}`);
    console.log(`   ID: ${video.id}`);
    console.log(`   Source: ${source}`);
    if (video.metadata?.description) {
      console.log(`   Description: ${video.metadata.description}`);
    }
    console.log('');
  });
  
  console.log(`Total videos: ${config.videos.length}`);
  console.log(`Enabled videos: ${config.videos.filter(v => v.enabled).length}`);
}

function reorderVideos(orderString) {
  const config = loadConfig();
  const videoIds = orderString.split(',').map(id => id.trim());
  
  console.log(`🔄 Reordering videos: ${videoIds.join(' → ')}`);
  
  // Update indices based on new order
  videoIds.forEach((id, newIndex) => {
    const video = config.videos.find(v => v.id === id);
    if (video) {
      video.index = newIndex;
      console.log(`   ${video.title}: index ${video.index} → ${newIndex}`);
    } else {
      console.warn(`⚠️  Video with ID "${id}" not found`);
    }
  });
  
  saveConfig(config);
}

function addVideo(options) {
  const config = loadConfig();
  const maxIndex = Math.max(...config.videos.map(v => v.index), -1);
  
  const newVideo = {
    id: options.id,
    index: maxIndex + 1,
    title: options.title,
    enabled: true
  };
  
  if (options.path) {
    newVideo.path = options.path;
  } else if (options.url) {
    newVideo.url = options.url;
    newVideo.durationInSeconds = parseFloat(options.duration) || config.settings.defaultDuration;
  } else {
    console.error('❌ Either --path or --url must be specified');
    process.exit(1);
  }
  
  if (options.description) {
    newVideo.metadata = { description: options.description };
  }
  
  config.videos.push(newVideo);
  
  console.log(`✅ Added video: ${newVideo.title} (index: ${newVideo.index})`);
  saveConfig(config);
}

function toggleVideo(videoId, enabled) {
  const config = loadConfig();
  const video = config.videos.find(v => v.id === videoId);
  
  if (!video) {
    console.error(`❌ Video with ID "${videoId}" not found`);
    process.exit(1);
  }
  
  video.enabled = enabled;
  const status = enabled ? 'enabled' : 'disabled';
  console.log(`✅ Video "${video.title}" ${status}`);
  
  saveConfig(config);
}

function parseArgs() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'list':
      listVideos();
      break;
      
    case 'reorder':
      if (!args[1]) {
        console.error('❌ Usage: reorder "id1,id2,id3"');
        process.exit(1);
      }
      reorderVideos(args[1]);
      break;
      
    case 'add':
      const options = {};
      args.slice(1).forEach(arg => {
        if (arg.startsWith('--')) {
          const [key, value] = arg.substring(2).split('=');
          options[key] = value;
        }
      });
      
      if (!options.id || !options.title) {
        console.error('❌ Usage: add --id="video-id" --title="Video Title" --path="video.mp4" [--description="..."]');
        process.exit(1);
      }
      
      addVideo(options);
      break;
      
    case 'enable':
      if (!args[1]) {
        console.error('❌ Usage: enable "video-id"');
        process.exit(1);
      }
      toggleVideo(args[1], true);
      break;
      
    case 'disable':
      if (!args[1]) {
        console.error('❌ Usage: disable "video-id"');
        process.exit(1);
      }
      toggleVideo(args[1], false);
      break;
      
    default:
      console.log(`
📹 Video Configuration Manager

Usage:
  node scripts/manage-video-config.js <command> [options]

Commands:
  list                                    List all videos
  reorder "id1,id2,id3"                  Reorder videos by IDs
  add --id="..." --title="..." --path="..." Add new video
  enable "video-id"                       Enable video
  disable "video-id"                      Disable video

Examples:
  node scripts/manage-video-config.js list
  node scripts/manage-video-config.js reorder "sabo-power,ace-intro,luffy-dragon"
  node scripts/manage-video-config.js add --id="new-video" --title="New Video" --path="new.mp4"
  node scripts/manage-video-config.js disable "luffy-dragon"
      `);
      break;
  }
}

// Run the script
parseArgs();
