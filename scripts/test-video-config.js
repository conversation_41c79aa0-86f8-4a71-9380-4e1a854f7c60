#!/usr/bin/env node

/**
 * Test Video Configuration
 * Validates JSON config and tests loading functionality
 */

const fs = require('fs');
const path = require('path');

const CONFIG_PATH = path.join(__dirname, '../src/config/videoConfig.json');
const VIDEOS_PATH = path.join(__dirname, '../public/videos');

function testConfig() {
  console.log('🧪 Testing Video Configuration...\n');
  
  // Test 1: JSON validity
  console.log('1️⃣ Testing JSON validity...');
  let config;
  try {
    const content = fs.readFileSync(CONFIG_PATH, 'utf8');
    config = JSON.parse(content);
    console.log('   ✅ JSON is valid');
  } catch (error) {
    console.log('   ❌ JSON is invalid:', error.message);
    return;
  }
  
  // Test 2: Required fields
  console.log('\n2️⃣ Testing required fields...');
  const requiredFields = ['version', 'fps', 'videos', 'settings'];
  const missingFields = requiredFields.filter(field => !config[field]);
  
  if (missingFields.length === 0) {
    console.log('   ✅ All required fields present');
  } else {
    console.log('   ❌ Missing fields:', missingFields.join(', '));
  }
  
  // Test 3: Video validation
  console.log('\n3️⃣ Testing video entries...');
  const errors = [];
  
  config.videos.forEach((video, index) => {
    const videoErrors = [];
    
    // Required fields
    if (!video.id) videoErrors.push('missing id');
    if (typeof video.index !== 'number') videoErrors.push('missing/invalid index');
    if (!video.title) videoErrors.push('missing title');
    if (typeof video.enabled !== 'boolean') videoErrors.push('missing/invalid enabled');
    
    // Source validation
    if (!video.path && !video.url) {
      videoErrors.push('missing both path and url');
    }
    
    // Local file validation
    if (video.path) {
      const filePath = path.join(VIDEOS_PATH, video.path);
      if (!fs.existsSync(filePath)) {
        videoErrors.push(`file not found: ${video.path}`);
      }
    }
    
    if (videoErrors.length > 0) {
      errors.push(`Video ${index} (${video.id || 'no-id'}): ${videoErrors.join(', ')}`);
    }
  });
  
  if (errors.length === 0) {
    console.log('   ✅ All videos valid');
  } else {
    console.log('   ❌ Video errors:');
    errors.forEach(error => console.log(`      ${error}`));
  }
  
  // Test 4: Index uniqueness
  console.log('\n4️⃣ Testing index uniqueness...');
  const indices = config.videos.map(v => v.index);
  const duplicateIndices = indices.filter((index, pos) => indices.indexOf(index) !== pos);
  
  if (duplicateIndices.length === 0) {
    console.log('   ✅ All indices unique');
  } else {
    console.log('   ❌ Duplicate indices:', [...new Set(duplicateIndices)].join(', '));
  }
  
  // Test 5: ID uniqueness
  console.log('\n5️⃣ Testing ID uniqueness...');
  const ids = config.videos.map(v => v.id).filter(Boolean);
  const duplicateIds = ids.filter((id, pos) => ids.indexOf(id) !== pos);
  
  if (duplicateIds.length === 0) {
    console.log('   ✅ All IDs unique');
  } else {
    console.log('   ❌ Duplicate IDs:', [...new Set(duplicateIds)].join(', '));
  }
  
  // Summary
  console.log('\n📊 Summary:');
  console.log(`   Total videos: ${config.videos.length}`);
  console.log(`   Enabled videos: ${config.videos.filter(v => v.enabled).length}`);
  console.log(`   Local files: ${config.videos.filter(v => v.path).length}`);
  console.log(`   External URLs: ${config.videos.filter(v => v.url).length}`);
  
  const enabledVideos = config.videos
    .filter(v => v.enabled)
    .sort((a, b) => a.index - b.index);
  
  console.log('\n🎬 Playback order:');
  enabledVideos.forEach((video, index) => {
    const source = video.path ? `📁 ${video.path}` : `🌐 ${video.url}`;
    console.log(`   ${index + 1}. ${video.title} (${source})`);
  });
  
  console.log('\n✅ Configuration test completed!');
}

// Run the test
testConfig();
