import { videos } from '../video-list';

// Type definitions for ShortVideo props
export interface ShortVideoProps {
  // Video title customization
  videoTitles?: string[];
  
  // Display toggles
  showVideoTitles?: boolean;
  showVideoCounter?: boolean;
  showProgressBar?: boolean;
  showLogo?: boolean;
  
  // Styling options
  titleStyle?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold' | 'bolder' | 'lighter';
    color?: string;
    backgroundColor?: string;
    borderRadius?: number;
  };
}

// Helper function to get video titles with fallback to filenames
export function getVideoTitles(props: Partial<ShortVideoProps>): string[] {
  // If custom titles are provided, use them
  if (props.videoTitles && Array.isArray(props.videoTitles)) {
    // Ensure we have titles for all videos, fill missing ones with filenames
    const titles = [...props.videoTitles];
    while (titles.length < videos.length) {
      const videoIndex = titles.length;
      const video = videos[videoIndex];
      titles.push(video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${videoIndex + 1}`);
    }
    return titles;
  }
  
  // Default: use filenames without extension
  return videos.map((video, index) =>
    video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${index + 1}`
  );
}

// Helper function to get default video titles (for schema defaults)
export function getDefaultVideoTitles(): string[] {
  return videos.map((video, index) =>
    video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${index + 1}`
  );
}
