import { videos } from '../auto-generated/video-list';
import { getVideoTitles as getConfigTitles } from '../videoConfig';

// Type definitions for ShortVideo props
export interface ShortVideoProps {
  // Video configuration mode
  useJsonConfig?: boolean;       // Use JSON config instead of auto-generated list

  // Video title customization
  videoTitles?: string[];

  // Video order customization (for JSON config)
  videoOrder?: string[];         // Array of video IDs in desired order

  // Display toggles
  showVideoTitles?: boolean;
  showVideoCounter?: boolean;
  showProgressBar?: boolean;
  showLogo?: boolean;

  // Styling options
  titleStyle?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold' | 'bolder' | 'lighter';
    color?: string;
    backgroundColor?: string;
    borderRadius?: number;
  };
}

// Helper function to get video titles with fallback to filenames
export function getVideoTitles(props: Partial<ShortVideoProps>): string[] {
  // If using JSON config
  if (props.useJsonConfig) {
    try {
      const configTitles = getConfigTitles();

      // If custom titles provided, use them but ensure we have enough
      if (props.videoTitles && Array.isArray(props.videoTitles)) {
        const titles = [...props.videoTitles];
        while (titles.length < configTitles.length) {
          titles.push(configTitles[titles.length] || `Video ${titles.length + 1}`);
        }
        return titles;
      }

      return configTitles;
    } catch (error) {
      console.error('Error loading titles from config:', error);
      // Fallback to auto-generated list
    }
  }

  // If custom titles are provided, use them
  if (props.videoTitles && Array.isArray(props.videoTitles)) {
    // Ensure we have titles for all videos, fill missing ones with filenames
    const titles = [...props.videoTitles];
    while (titles.length < videos.length) {
      const videoIndex = titles.length;
      const video = videos[videoIndex];
      titles.push(video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${videoIndex + 1}`);
    }
    return titles;
  }

  // Default: use filenames without extension
  return videos.map((video, index) =>
    video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${index + 1}`
  );
}

// Helper function to get default video titles (for schema defaults)
export function getDefaultVideoTitles(): string[] {
  try {
    // Try config first
    return getConfigTitles();
  } catch (error) {
    // Fallback to auto-generated list
    return videos.map((video, index) =>
      video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${index + 1}`
    );
  }
}
