import { z } from 'zod';
import { getDefaultVideoTitles } from './nativeSchema';

// Get default titles based on current video filenames
const defaultTitles = getDefaultVideoTitles();

// Remotion schema definition using Zod
// Note: You need to install zod first: yarn add zod@3.22.3 @remotion/zod-types@4.0.311
export const shortVideoSchema = z.object({
  // Video titles - each video gets its own editable title field
  videoTitles: z.array(z.string()).default(defaultTitles).describe('Custom titles for each video'),

  // Display options
  showVideoTitles: z.boolean().default(true).describe('Show video titles overlay'),
  showVideoCounter: z.boolean().default(false).describe('Show video counter (1/3, 2/3, etc.)'),
  showProgressBar: z.boolean().default(true).describe('Show progress bar'),
  showLogo: z.boolean().default(true).describe('Show logo overlay'),

  // Title styling
  titleStyle: z.object({
    fontSize: z.number().min(12).max(72).default(32).describe('Title font size'),
    fontWeight: z.enum(['normal', 'bold', 'bolder', 'lighter']).default('bold').describe('Title font weight'),
    color: z.string().default('#ffffff').describe('Title text color'),
    backgroundColor: z.string().default('rgba(0, 0, 0, 0.7)').describe('Title background color'),
    borderRadius: z.number().min(0).max(50).default(10).describe('Title border radius'),
  }).default({
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 10,
  }).describe('Title styling options'),
});
