import React from 'react';
import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
  Sequence,
  Video,
  staticFile,
  interpolate,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './auto-generated/video-list';
import { getOrderedVideos } from './videoConfig';
import { VideoCounter, VideoTitle, ProgressBar, Watermark, Logo } from './components/VideoOverlays';
import { shortVideoConfig } from './config/shortVideoConfig';
import { ShortVideoProps, getVideoTitles } from './schema/nativeSchema';

// Simple video component with fade effects
const VideoWithFade: React.FC<{
  video: {
    filename: string;
    durationInFrames: number;
    durationInSeconds: number;
  };
  fadeIn: boolean;
  fadeOut: boolean;
  transitionDuration: number;
}> = ({ video, fadeIn, fadeOut, transitionDuration }) => {
  const frame = useCurrentFrame();

  let opacity = 1;

  // Fade in at the beginning
  if (fadeIn && frame < transitionDuration) {
    opacity = interpolate(
      frame,
      [0, transitionDuration],
      [0, 1],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
  }

  // Fade out at the end
  if (fadeOut && frame >= video.durationInFrames - transitionDuration) {
    opacity = interpolate(
      frame,
      [video.durationInFrames - transitionDuration, video.durationInFrames],
      [1, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
  }

  return (
    <Video
      src={staticFile(`videos/${video.filename}`)}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        opacity,
      }}
    />
  );
};

export const ShortVideo: React.FC<Partial<ShortVideoProps>> = (props = {}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const config = shortVideoConfig;

  // Determine which video source to use
  const useJsonConfig = props?.useJsonConfig ?? false;

  // Get videos based on configuration mode
  const currentVideos = useJsonConfig ? getOrderedVideos() : videos;
  const currentTotalDuration = useJsonConfig
    ? currentVideos.reduce((sum, v) => sum + v.durationInFrames, 0)
    : totalVideoDurationInFrames;

  // Get custom video titles from props with safe fallback
  let videoTitles: string[] = [];
  try {
    videoTitles = getVideoTitles(props);
  } catch (error) {
    console.error('Error getting video titles:', error);
    // Fallback to default titles
    videoTitles = currentVideos.map((video, index) =>
      video?.filename?.replace(/\.[^/.]+$/, '') || video?.title || `Video ${index + 1}`
    );
  }

  // Safe access to props with defaults
  const showVideoTitles = props?.showVideoTitles ?? true;
  const showVideoCounter = props?.showVideoCounter ?? false;
  const showProgressBar = props?.showProgressBar ?? true;
  const showLogo = props?.showLogo ?? true;
  const titleStyle = props?.titleStyle ?? {
    fontSize: 32,
    fontWeight: 'bold' as const,
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 10,
  };

  // If no videos available, show placeholder
  if (currentVideos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            {useJsonConfig
              ? 'Check your videoConfig.ts file'
              : 'Add video files to public/videos/ folder'
            }
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing for overlays
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;

  for (let i = 0; i < currentVideos.length; i++) {
    if (frame < accumulatedFrames + currentVideos[i].durationInFrames) {
      currentVideoIndex = i;
      break;
    }
    accumulatedFrames += currentVideos[i].durationInFrames;
  }

  // Handle case where frame exceeds total video duration
  if (frame >= currentTotalDuration) {
    currentVideoIndex = currentVideos.length - 1;
  }

  const currentVideo = currentVideos[currentVideoIndex];

  return (
    <AbsoluteFill style={{ backgroundColor: config.backgroundColor }}>
      {/* Render each video in its own sequence */}
      {currentVideos.map((video, index) => {
        const isFirstVideo = index === 0;
        const isLastVideo = index === currentVideos.length - 1;
        const transitionDuration = config.transitionDurationInFrames;

        // Calculate start frame for each video
        const startFrame = currentVideos.slice(0, index).reduce((sum, v) => sum + v.durationInFrames, 0);

        return (
          <Sequence
            key={video.filename || (video as any).id || `video-${index}`}
            from={startFrame}
            durationInFrames={video.durationInFrames}
          >
            <VideoWithFade
              video={video}
              fadeIn={!isFirstVideo} // Fade in for all videos except first
              fadeOut={!isLastVideo} // Fade out for all videos except last
              transitionDuration={transitionDuration}
            />
          </Sequence>
        );
      })}

      {/* Overlays */}
      {config.overlays.videoCounter.enabled && showVideoCounter && (
        <VideoCounter
          current={currentVideoIndex + 1}
          total={currentVideos.length}
          style={{
            ...config.overlays.videoCounter.position,
            ...config.overlays.videoCounter.style
          }}
        />
      )}

      {config.overlays.videoTitle.enabled && showVideoTitles && (
        <VideoTitle
          title={videoTitles[currentVideoIndex] || (currentVideo as any)?.title || currentVideo?.filename?.replace(/\.[^/.]+$/, '') || `Video ${currentVideoIndex + 1}`}
          style={{
            ...config.overlays.videoTitle.position,
            ...config.overlays.videoTitle.style,
            fontSize: titleStyle.fontSize,
            fontWeight: titleStyle.fontWeight,
            color: titleStyle.color,
            backgroundColor: titleStyle.backgroundColor,
            borderRadius: titleStyle.borderRadius,
          }}
        />
      )}

      {config.overlays.progressBar.enabled && showProgressBar && (
        <ProgressBar
          progress={frame / durationInFrames}
          style={{
            ...config.overlays.progressBar.position,
            height: config.overlays.progressBar.height
          }}
          barColor={config.overlays.progressBar.barColor}
          backgroundColor={config.overlays.progressBar.backgroundColor}
        />
      )}

      {config.overlays.watermark.enabled && (
        <Watermark
          text={config.overlays.watermark.text}
          position={config.overlays.watermark.position}
          style={config.overlays.watermark.style}
        />
      )}

      {config.overlays.logo.enabled && showLogo && (
        <Logo
          src={config.overlays.logo.src}
          position={config.overlays.logo.position}
          size={config.overlays.logo.size}
          offset={config.overlays.logo.offset}
          opacity={config.overlays.logo.opacity}
          style={config.overlays.logo.style}
        />
      )}
    </AbsoluteFill>
  );
};
