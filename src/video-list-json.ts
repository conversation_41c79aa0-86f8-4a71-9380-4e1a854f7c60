// JSON-based video list
import { loadVideoConfiguration } from './utils/videoLoader';
import { ProcessedVideoItem } from './types/videoTypes';

// Cache for loaded videos
let cachedVideos: ProcessedVideoItem[] | null = null;
let cachedTotalDuration: number = 0;

/**
 * Load videos from JSON configuration
 * This replaces the auto-generated video-list.ts
 */
export async function loadVideosFromConfig() {
  if (cachedVideos) {
    return {
      videos: cachedVideos,
      totalVideoDurationInFrames: cachedTotalDuration
    };
  }

  try {
    const result = await loadVideoConfiguration();
    
    if (result.errors.length > 0) {
      console.warn('Video loading errors:', result.errors);
    }

    cachedVideos = result.videos;
    cachedTotalDuration = result.totalDurationInFrames;

    return {
      videos: result.videos,
      totalVideoDurationInFrames: result.totalDurationInFrames
    };
  } catch (error) {
    console.error('Failed to load video configuration:', error);
    
    // Fallback to empty array
    return {
      videos: [] as ProcessedVideoItem[],
      totalVideoDurationInFrames: 0
    };
  }
}

/**
 * Synchronous access to cached videos
 * Call loadVideosFromConfig() first to populate cache
 */
export function getVideosSync(): ProcessedVideoItem[] {
  if (!cachedVideos) {
    console.warn('Videos not loaded yet. Call loadVideosFromConfig() first.');
    return [];
  }
  return cachedVideos;
}

/**
 * Get total duration synchronously
 */
export function getTotalDurationSync(): number {
  return cachedTotalDuration;
}

/**
 * Clear cache (useful for hot reloading)
 */
export function clearVideoCache() {
  cachedVideos = null;
  cachedTotalDuration = 0;
}

/**
 * Convert ProcessedVideoItem to legacy format for compatibility
 */
export function toLegacyFormat(videos: ProcessedVideoItem[]) {
  return videos.map(video => ({
    filename: video.filename || `${video.id}.mp4`,
    durationInFrames: video.durationInFrames,
    durationInSeconds: video.durationInSeconds,
    // Additional fields for enhanced functionality
    id: video.id,
    title: video.title,
    source: video.source,
    url: video.url
  }));
}
