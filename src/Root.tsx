import "./index.css";
import { Composition } from "remotion";
import { MusicVideo } from "./MusicVideo";
import { ShortVideo } from "./ShortVideo";
import { audioDurationInFrames } from "./audio-duration";
import { totalVideoDurationInFrames } from "./video-list";
import { shortVideoConfig } from "./config/shortVideoConfig";
import { shortVideoSchema } from "./schema/shortVideoSchema";
import { getDefaultVideoTitles } from "./schema/nativeSchema";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="MusicVideo"
        component={MusicVideo}
        durationInFrames={audioDurationInFrames}
        fps={30}
        width={1920}
        height={1080}
      />
      <Composition
        id="ShortVideo"
        component={ShortVideo}
        durationInFrames={totalVideoDurationInFrames || 1800} // Default 60 seconds if no videos
        fps={shortVideoConfig.fps}
        width={shortVideoConfig.width}
        height={shortVideoConfig.height}
        schema={shortVideoSchema} // Uncomment after installing Zod
        defaultProps={{
          videoTitles: ["Ace", "Luffy", "Sabo"],
          showVideoTitles: true,
          showVideoCounter: false,
          showProgressBar: false,
          showLogo: true,
          titleStyle: {
            fontSize: 72,
            fontWeight: "bold" as const,
            color: "#ffffff",
            backgroundColor: "rgba(0, 0, 0, 0)",
            borderRadius: 10,
          },
        }}
      />
    </>
  );
};
