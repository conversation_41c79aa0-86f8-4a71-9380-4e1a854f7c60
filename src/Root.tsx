import "./index.css";
import { Composition } from "remotion";
import { MusicVideo } from "./compositions/MusicVideo";
import { ShortVideo } from "./compositions/ShortVideo";
import { audioDurationInFrames } from './auto-generated/audio-duration';
import { totalVideoDurationInFrames } from './auto-generated/video-list';
import { shortVideoConfig } from './config/shortVideoConfig';
import { shortVideoSchema } from "./schema/shortVideoSchema";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="MusicVideo"
        component={MusicVideo}
        durationInFrames={audioDurationInFrames}
        fps={30}
        width={1920}
        height={1080}
      />
      <Composition
        id="ShortVideo"
        component={ShortVideo}
        durationInFrames={totalVideoDurationInFrames || 1800} // Default 60 seconds if no videos
        fps={shortVideoConfig.fps}
        width={shortVideoConfig.width}
        height={shortVideoConfig.height}
        schema={shortVideoSchema}
        defaultProps={{
          videoTitles: [],
          showVideoTitles: true,
          showVideoCounter: false,
          showProgressBar: false,
          showLogo: true,
          titleStyle: {
            fontSize: 72,
            fontWeight: "bold" as const,
            color: "#ffffff",
            backgroundColor: "rgba(0, 0, 0, 0)",
            borderRadius: 10,
          },
        }}
      />
    </>
  );
};
