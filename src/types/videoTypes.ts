// Video configuration types
export interface VideoItem {
  id: string;                    // Unique identifier
  index: number;                 // Display order (0-based)
  title: string;                 // Display title
  path?: string;                 // Local file path (relative to public/videos/)
  url?: string;                  // External URL
  enabled: boolean;              // Whether to include in composition
  durationInSeconds?: number;    // Duration (auto-calculated for local files)
  durationInFrames?: number;     // Duration in frames (auto-calculated)
  metadata?: {
    description?: string;
    tags?: string[];
    thumbnail?: string;
  };
}

export interface VideoConfiguration {
  version: string;               // Config version for migration
  fps: number;                   // Frame rate for calculations
  videos: VideoItem[];           // Array of video items
  settings: {
    autoSort: boolean;           // Auto-sort by index
    allowDuplicates: boolean;    // Allow same video multiple times
    defaultDuration: number;     // Default duration for URLs (seconds)
  };
}

// Processed video item (after loading metadata)
export interface ProcessedVideoItem extends VideoItem {
  durationInSeconds: number;
  durationInFrames: number;
  source: 'local' | 'url';
  filename?: string;             // For local files
}

// Video loading result
export interface VideoLoadResult {
  videos: ProcessedVideoItem[];
  totalDurationInFrames: number;
  errors: Array<{
    videoId: string;
    error: string;
  }>;
}
