// Debug script for JSON config
import { loadVideoConfiguration, getVideoTitlesFromConfig } from './utils/videoLoader';

export async function debugJsonConfig() {
  console.log('🐛 DEBUG: Testing JSON Config...');
  
  try {
    // Test 1: Load video titles
    console.log('\n1️⃣ Testing getVideoTitlesFromConfig...');
    const titles = getVideoTitlesFromConfig();
    console.log('✅ Titles:', titles);
    
    // Test 2: Load full configuration
    console.log('\n2️⃣ Testing loadVideoConfiguration...');
    const result = await loadVideoConfiguration();
    console.log('✅ Result:', result);
    console.log('📊 Videos loaded:', result.videos.length);
    console.log('⏱️ Total duration:', result.totalDurationInFrames, 'frames');
    
    if (result.errors.length > 0) {
      console.log('⚠️ Errors:', result.errors);
    }
    
    // Test 3: Show processed videos
    console.log('\n3️⃣ Processed videos:');
    result.videos.forEach((video, index) => {
      console.log(`   ${index}: ${video.title} (${video.durationInFrames} frames)`);
      console.log(`      Source: ${video.source} - ${video.filename || video.url}`);
    });
    
    return result;
    
  } catch (error) {
    console.error('❌ Error in debugJsonConfig:', error);
    throw error;
  }
}

// Export for use in components
export { debugJsonConfig as default };
