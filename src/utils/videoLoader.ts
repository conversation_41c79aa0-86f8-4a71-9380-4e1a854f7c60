import { VideoConfiguration, VideoItem, ProcessedVideoItem, VideoLoadResult } from '../types/videoTypes';
// Import JSON config - may need to be dynamic import
// import videoConfig from '../config/videoConfig.json';

// Temporary: inline config for testing
const videoConfig = {
  "version": "1.0.0",
  "fps": 30,
  "videos": [
    {
      "id": "ace-child",
      "index": 0,
      "title": "Portgas D. Ace",
      "path": "1-ace-child.mp4",
      "enabled": true
    },
    {
      "id": "ace-intro",
      "index": 1,
      "title": "Do you a fan of Ace - Fire Fist ?",
      "path": "2-ace.mp4",
      "enabled": true
    },
    {
      "id": "luffy-child",
      "index": 2,
      "title": "Or Straw Hat Luffy ?",
      "path": "3-luffy-child.mp4",
      "enabled": true
    },
    {
      "id": "luffy-dragon",
      "index": 3,
      "title": "Monkey D. Luffy",
      "path": "4-luffy.mp4",
      "enabled": true
    },
    {
      "id": "sabo-power",
      "index": 4,
      "title": "Sabo",
      "path": "sabo.mp4",
      "enabled": true
    },
    {
      "id": "sabo-chief",
      "index": 5,
      "title": "Chief of Staff",
      "path": "sabo.mp4",
      "enabled": true
    },
    {
      "id": "all-family",
      "index": 6,
      "title": "We are a family",
      "path": "7-all.mp4",
      "enabled": true
    }
  ],
  "settings": {
    "autoSort": true,
    "allowDuplicates": false,
    "defaultDuration": 5.0
  }
};

// Import the auto-generated video list as fallback
import { videos as autoGeneratedVideos } from '../video-list';

/**
 * Load and process video configuration
 */
export async function loadVideoConfiguration(): Promise<VideoLoadResult> {
  console.log('🔄 Loading video configuration from JSON...');
  const config = videoConfig as VideoConfiguration;
  console.log('📋 Config loaded:', config);

  const processedVideos: ProcessedVideoItem[] = [];
  const errors: Array<{ videoId: string; error: string }> = [];
  let totalDurationInFrames = 0;

  // Filter enabled videos and sort by index
  const enabledVideos = config.videos
    .filter(video => video.enabled)
    .sort((a, b) => a.index - b.index);

  console.log('🎥 Enabled videos:', enabledVideos.length);
  console.log('📋 Enabled videos list:', enabledVideos.map(v => `${v.index}: ${v.title}`));

  for (const videoItem of enabledVideos) {
    try {
      const processedVideo = await processVideoItem(videoItem, config.fps);
      processedVideos.push(processedVideo);
      totalDurationInFrames += processedVideo.durationInFrames;
    } catch (error) {
      console.error(`Error processing video ${videoItem.id}:`, error);
      errors.push({
        videoId: videoItem.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return {
    videos: processedVideos,
    totalDurationInFrames,
    errors
  };
}

/**
 * Process individual video item
 */
async function processVideoItem(videoItem: VideoItem, fps: number): Promise<ProcessedVideoItem> {
  console.log(`🔍 Processing video: ${videoItem.id} - ${videoItem.title}`);

  let durationInSeconds: number;
  let source: 'local' | 'url';
  let filename: string | undefined;

  if (videoItem.path) {
    // Local file - try to get duration from auto-generated list
    source = 'local';
    filename = videoItem.path;

    const autoVideo = autoGeneratedVideos.find(v => v.filename === videoItem.path);
    if (autoVideo) {
      durationInSeconds = autoVideo.durationInSeconds;
    } else {
      // Fallback to provided duration or default
      durationInSeconds = videoItem.durationInSeconds || videoConfig.settings.defaultDuration;
      console.warn(`Duration not found for ${videoItem.path}, using fallback: ${durationInSeconds}s`);
    }
  } else if (videoItem.url) {
    // External URL
    source = 'url';
    durationInSeconds = videoItem.durationInSeconds || videoConfig.settings.defaultDuration;
  } else {
    throw new Error(`Video ${videoItem.id} must have either path or url`);
  }

  const durationInFrames = Math.floor(durationInSeconds * fps);

  return {
    ...videoItem,
    durationInSeconds,
    durationInFrames,
    source,
    filename
  };
}

/**
 * Get video titles in order
 */
export function getVideoTitlesFromConfig(): string[] {
  const config = videoConfig as VideoConfiguration;
  return config.videos
    .filter(video => video.enabled)
    .sort((a, b) => a.index - b.index)
    .map(video => video.title);
}

/**
 * Get video by index
 */
export function getVideoByIndex(index: number): VideoItem | undefined {
  const config = videoConfig as VideoConfiguration;
  const enabledVideos = config.videos
    .filter(video => video.enabled)
    .sort((a, b) => a.index - b.index);

  return enabledVideos[index];
}

/**
 * Update video order
 */
export function reorderVideos(videoIds: string[]): VideoConfiguration {
  const config = { ...videoConfig } as VideoConfiguration;

  // Update indices based on new order
  videoIds.forEach((id, newIndex) => {
    const video = config.videos.find(v => v.id === id);
    if (video) {
      video.index = newIndex;
    }
  });

  return config;
}

/**
 * Add new video to configuration
 */
export function addVideoToConfig(newVideo: Omit<VideoItem, 'index'>): VideoConfiguration {
  const config = { ...videoConfig } as VideoConfiguration;
  const maxIndex = Math.max(...config.videos.map(v => v.index), -1);

  const videoWithIndex: VideoItem = {
    ...newVideo,
    index: maxIndex + 1
  };

  config.videos.push(videoWithIndex);
  return config;
}

/**
 * Remove video from configuration
 */
export function removeVideoFromConfig(videoId: string): VideoConfiguration {
  const config = { ...videoConfig } as VideoConfiguration;
  config.videos = config.videos.filter(v => v.id !== videoId);

  // Reindex remaining videos if autoSort is enabled
  if (config.settings.autoSort) {
    config.videos
      .sort((a, b) => a.index - b.index)
      .forEach((video, index) => {
        video.index = index;
      });
  }

  return config;
}

/**
 * Toggle video enabled state
 */
export function toggleVideoEnabled(videoId: string): VideoConfiguration {
  const config = { ...videoConfig } as VideoConfiguration;
  const video = config.videos.find(v => v.id === videoId);

  if (video) {
    video.enabled = !video.enabled;
  }

  return config;
}
