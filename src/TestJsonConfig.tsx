import React from 'react';
import { AbsoluteFill } from 'remotion';
import debugJsonConfig from './debug-json-config';

export const TestJsonConfig: React.FC = () => {
  const [result, setResult] = React.useState<any>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    debugJsonConfig()
      .then(result => {
        setResult(result);
        setLoading(false);
      })
      .catch(error => {
        setError(error.message);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', color: 'white', padding: 40 }}>
        <h1>Loading JSON Config...</h1>
      </AbsoluteFill>
    );
  }

  if (error) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', color: 'red', padding: 40 }}>
        <h1>Error Loading JSON Config</h1>
        <p>{error}</p>
      </AbsoluteFill>
    );
  }

  return (
    <AbsoluteFill style={{ backgroundColor: '#000', color: 'white', padding: 40 }}>
      <h1>JSON Config Test Results</h1>
      <div style={{ marginTop: 20 }}>
        <h2>Videos Loaded: {result?.videos?.length || 0}</h2>
        <h3>Total Duration: {result?.totalDurationInFrames || 0} frames</h3>
        
        <div style={{ marginTop: 20 }}>
          <h3>Videos:</h3>
          {result?.videos?.map((video: any, index: number) => (
            <div key={index} style={{ marginBottom: 10, fontSize: 14 }}>
              <strong>{index + 1}. {video.title}</strong>
              <br />
              <span>Source: {video.source} - {video.filename || video.url}</span>
              <br />
              <span>Duration: {video.durationInFrames} frames</span>
            </div>
          ))}
        </div>
        
        {result?.errors?.length > 0 && (
          <div style={{ marginTop: 20, color: 'orange' }}>
            <h3>Errors:</h3>
            {result.errors.map((error: any, index: number) => (
              <div key={index}>
                {error.videoId}: {error.error}
              </div>
            ))}
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};
