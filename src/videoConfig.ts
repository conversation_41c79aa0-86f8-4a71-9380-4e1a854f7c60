// Find duration from auto-generated list
import { videos } from "./video-list";

export const videoConfig = [
  {
    index: 0,
    title: "Portgas D. Ace",
    path: "1-ace-child.mp4"
  },
  {
    index: 1,
    title: "Do you a fan of Ace - Fire Fist ?",
    path: "2-ace.mp4"
  },
  {
    index: 2,
    title: "Or Straw Hat Luffy ?",
    path: "3-luffy-child.mp4"
  },
  {
    index: 3,
    title: "Monkey D. Luffy",
    path: "4-luffy.mp4"
  },
  {
    index: 4,
    title: "Or Sabo?",
    path: "5-sabo-child.mp4"
  },
  {
    index: 5,
    title: "A Chief of Staff",
    path: "6-sabo.mp4"
  },
  {
    index: 6,
    title: "We are a family",
    path: "7-all.mp4"
  }
];

// Get videos in order (array order)
export function getOrderedVideos() {
  if (!videoConfig || !Array.isArray(videoConfig)) {
    console.error('videoConfig is not defined or not an array');
    return [];
  }

  return videoConfig.map(config => {
    const autoVideo = videos?.find((v: any) => v.filename === config.path);

    return {
      filename: config.path,
      title: config.title,
      durationInFrames: autoVideo?.durationInFrames || 150, // fallback
      durationInSeconds: autoVideo?.durationInSeconds || 5
    };
  });
}

// Get titles only
export function getVideoTitles() {
  return videoConfig
    .sort((a, b) => a.index - b.index)
    .map(config => config.title);
}
