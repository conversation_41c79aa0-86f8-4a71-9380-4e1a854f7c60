// Simple video configuration - order by array position

// Find duration from auto-generated list
import { videos } from "./auto-generated/video-list";

export const videoConfig = [
  {
    title: "Portgas D. Ace",
    path: "1-ace-child.mp4"
  },
  {
    title: "Do you a fan of Fire Fist?",
    path: "2-ace.mp4"
  },
  {
    title: "Or Straw Hat Luffy?",
    path: "3-luffy-child.mp4"
  },
  {
    title: "Monkey D. Luffy",
    path: "4-luffy.mp4"
  },
  {
    title: "Or Sabo?",
    path: "5-sabo-child.mp4"
  },
  {
    title: "Chief of Staff",
    path: "6-sabo.mp4"
  },
  {
    title: "We are a family",
    path: "7-all.mp4"
  }
];

// Get videos in order (array order)
export function getOrderedVideos() {
  return videoConfig.map(config => {
    const autoVideo = videos.find((v: any) => v.filename === config.path);

    return {
      filename: config.path,
      title: config.title,
      durationInFrames: autoVideo?.durationInFrames || 150, // fallback
      durationInSeconds: autoVideo?.durationInSeconds || 5
    };
  });
}

// Get titles only (array order)
export function getVideoTitles() {
  return videoConfig.map(config => config.title);
}
