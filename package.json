{"name": "music-video", "version": "1.0.0", "description": "My Remotion video", "repository": {}, "license": "UNLICENSED", "private": true, "dependencies": {"@remotion/cli": "4.0.310", "@remotion/media-parser": "4.0.310", "@remotion/media-utils": "^4.0.310", "@remotion/tailwind-v4": "4.0.310", "@remotion/zod-types": "4.0.311", "music-metadata": "^11.2.3", "react": "19.0.0", "react-dom": "19.0.0", "remotion": "4.0.310", "tailwindcss": "4.0.0", "zod": "^3.22.3"}, "devDependencies": {"@remotion/eslint-config-flat": "4.0.310", "@types/react": "19.0.0", "@types/web": "0.0.166", "eslint": "9.19.0", "prettier": "3.3.3", "ts-node": "^10.9.2", "typescript": "5.8.2"}, "scripts": {"prepare:assets": "ts-node scripts/prepare-assets.ts", "dev": "npm run prepare:assets && remotion studio", "build": "npm run prepare:assets && remotion bundle", "upgrade": "remotion upgrade", "lint": "eslint src && tsc"}, "sideEffects": ["*.css"]}