# Video Title Schema Setup

## Overview
Đã thêm schema support cho composition để cho phép edit title cho mỗi video. Schema sẽ tự động set default values là tên file video (không có extension).

## ⚠️ Current Status
**READY TO USE** - Schema structure đã được tạo và component đã được update để sử dụng video titles từ props.

**NEXT STEP**: Cài đặt Zod để enable visual editing trong Remotion Studio.

## Installation Required

Tr<PERSON>ớ<PERSON> khi sử dụng schema, bạn cần cài đặt các package cần thiết:

```bash
# Cài đặt Zod và Remotion Zod types
yarn add zod@3.22.3 @remotion/zod-types@4.0.311

# Hoặc với npm
npm install --save-exact zod@3.22.3 @remotion/zod-types@4.0.311
```

### Testing Without Zod (Current State)
Hiện tại bạn có thể test ngay mà không cần cài Zod:
- Component đã được update để sử dụng video titles từ props
- Default props đã được set với tên file videos
- Visual editing sẽ hoạt động sau khi cài Zod

## Features Added

### 1. **Video Title Editing**
- Mỗi video có thể có title riêng
- Default title = tên file (không có extension)
- Có thể edit trong Remotion Studio

### 2. **Display Controls**
- `showVideoTitles`: Hiện/ẩn video titles
- `showVideoCounter`: Hiện/ẩn counter (1/3, 2/3, etc.)
- `showProgressBar`: Hiện/ẩn progress bar
- `showLogo`: Hiện/ẩn logo

### 3. **Title Styling**
- `fontSize`: 12-72px (default: 32)
- `fontWeight`: normal, bold, bolder, lighter
- `color`: Text color (default: #ffffff)
- `backgroundColor`: Background color (default: rgba(0,0,0,0.7))
- `borderRadius`: 0-50px (default: 10)

## Current Video Files
Dựa trên video-list.ts hiện tại, các video titles mặc định sẽ là:
- "ace" (từ ace.mp4)
- "luffy-dragon" (từ luffy-dragon.mp4)
- "sabo" (từ sabo.mp4)

## Usage

### 1. **Trong Remotion Studio**
- Mở Remotion Studio: `yarn dev`
- Chọn "ShortVideo" composition
- Trong panel bên phải, bạn sẽ thấy các fields để edit:
  - Video Titles (array of strings)
  - Display options (checkboxes)
  - Title styling (sliders, color pickers, etc.)

### 2. **Programmatically**
```typescript
// Trong code, bạn có thể pass props như này:
const props = {
  videoTitles: ["Custom Title 1", "Custom Title 2", "Custom Title 3"],
  showVideoTitles: true,
  titleStyle: {
    fontSize: 40,
    color: "#ff0000",
    backgroundColor: "rgba(0,0,0,0.8)"
  }
};
```

## Files Created/Modified

### New Files:
- `src/schema/nativeSchema.ts` - Type definitions và helper functions
- `src/schema/shortVideoSchema.ts` - Zod schema definition

### Modified Files:
- `src/Root.tsx` - Added schema và defaultProps to composition
- `src/ShortVideo.tsx` - Updated to use schema props for video titles

## How It Works

1. **Schema Definition**: `shortVideoSchema.ts` defines the structure using Zod
2. **Default Values**: Automatically generated from current video filenames
3. **Type Safety**: TypeScript types derived from Zod schema
4. **Visual Editing**: Remotion Studio provides UI for editing all schema fields
5. **Fallback**: If no custom titles provided, falls back to filename-based titles

## Example Schema Values

```typescript
{
  videoTitles: ["ace", "luffy-dragon", "sabo"],
  showVideoTitles: true,
  showVideoCounter: false,
  showProgressBar: true,
  showLogo: true,
  titleStyle: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#ffffff",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 10
  }
}
```

## Next Steps

1. **Install dependencies**: `yarn add zod@3.22.3 @remotion/zod-types@4.0.311`
2. **Test the setup**: `yarn dev` và mở Remotion Studio
3. **Customize titles**: Edit video titles trong Studio UI
4. **Render video**: Với custom titles đã được set
