# JSON Video Configuration System

## Overview
Đã tạo hệ thống JSON để quản lý videos với index, title, path/url và có thể điều chỉnh thứ tự. Hệ thống hỗ trợ cả local files và external URLs.

## ✅ Features Implemented

### 1. **JSON Configuration**
- **File**: `src/config/videoConfig.json`
- **Định nghĩa**: index, title, path/url cho mỗi video
- **Metadata**: description, tags, thumbnail
- **Enable/disable**: từng video riêng biệt

### 2. **Video Types Support**
- **Local files**: từ `public/videos/` folder
- **External URLs**: từ internet
- **Mixed mode**: có thể mix cả hai loại

### 3. **Reordering System**
- **Index-based**: sắp xếp theo index number
- **Auto-sort**: tự động sắp xếp nếu enabled
- **Manual order**: c<PERSON> thể override thứ tự

### 4. **Backward Compatibility**
- **Fallback**: nếu JSON config fail, dùng auto-generated list
- **Legacy support**: vẫn hoạt động với hệ thống cũ

## 📁 Files Created

### Core Files:
- `src/types/videoTypes.ts` - Type definitions
- `src/config/videoConfig.json` - JSON configuration
- `src/utils/videoLoader.ts` - Loading utilities
- `src/video-list-json.ts` - JSON-based video list

### Updated Files:
- `src/schema/nativeSchema.ts` - Added JSON config support
- `src/ShortVideo.tsx` - Enhanced to use JSON config
- `src/Root.tsx` - Added useJsonConfig option

## 🔧 Usage

### 1. **Enable JSON Config**
Trong `src/Root.tsx`, set `useJsonConfig: true`:

```typescript
defaultProps={{
  useJsonConfig: true,  // Enable JSON config
  // ... other props
}}
```

### 2. **Configure Videos**
Edit `src/config/videoConfig.json`:

```json
{
  "version": "1.0.0",
  "fps": 30,
  "videos": [
    {
      "id": "ace-intro",
      "index": 0,
      "title": "Ace - Fire Fist",
      "path": "ace.mp4",
      "enabled": true,
      "metadata": {
        "description": "Portgas D. Ace introduction scene",
        "tags": ["one-piece", "ace", "fire"]
      }
    },
    {
      "id": "external-video",
      "index": 1,
      "title": "External Video",
      "url": "https://example.com/video.mp4",
      "enabled": true,
      "durationInSeconds": 10.0
    }
  ],
  "settings": {
    "autoSort": true,
    "allowDuplicates": false,
    "defaultDuration": 5.0
  }
}
```

### 3. **Video Properties**

#### Required:
- `id`: Unique identifier
- `index`: Display order (0-based)
- `title`: Display title
- `enabled`: true/false

#### Source (one required):
- `path`: Local file (relative to public/videos/)
- `url`: External URL

#### Optional:
- `durationInSeconds`: Duration (auto-detected for local files)
- `metadata`: Additional info

## 📋 Current JSON Config

Dựa trên videos hiện tại, JSON config mẫu:

```json
{
  "version": "1.0.0",
  "fps": 30,
  "videos": [
    {
      "id": "ace-intro",
      "index": 0,
      "title": "Ace - Fire Fist",
      "path": "ace.mp4",
      "enabled": true
    },
    {
      "id": "luffy-dragon",
      "index": 1,
      "title": "Luffy vs Dragon",
      "path": "luffy-dragon.mp4",
      "enabled": true
    },
    {
      "id": "sabo-power",
      "index": 2,
      "title": "Sabo's Power",
      "path": "sabo.mp4",
      "enabled": true
    }
  ]
}
```

## 🔄 Reordering Videos

### Method 1: Change Index
```json
{
  "id": "sabo-power",
  "index": 0,  // Move to first position
  "title": "Sabo's Power",
  "path": "sabo.mp4",
  "enabled": true
}
```

### Method 2: Disable Videos
```json
{
  "id": "luffy-dragon",
  "index": 1,
  "title": "Luffy vs Dragon",
  "path": "luffy-dragon.mp4",
  "enabled": false  // Skip this video
}
```

### Method 3: Add External Videos
```json
{
  "id": "youtube-video",
  "index": 3,
  "title": "YouTube Video",
  "url": "https://example.com/video.mp4",
  "enabled": true,
  "durationInSeconds": 15.0
}
```

## 🎛️ Schema Integration

Schema đã được update để hỗ trợ JSON config:

```typescript
interface ShortVideoProps {
  useJsonConfig?: boolean;       // Enable JSON config mode
  videoTitles?: string[];        // Override titles
  videoOrder?: string[];         // Override order by IDs
  // ... other props
}
```

## 🔍 Testing

### Test JSON Config:
1. Set `useJsonConfig: true` trong Root.tsx
2. Modify `videoConfig.json`
3. Run `yarn dev`
4. Check console logs for loading status

### Test Reordering:
1. Change `index` values trong JSON
2. Reload Remotion Studio
3. Videos sẽ hiển thị theo thứ tự mới

### Test External URLs:
1. Add video với `url` thay vì `path`
2. Set `durationInSeconds`
3. Test playback

## 🚨 Error Handling

### JSON Config Errors:
- **Invalid JSON**: Falls back to auto-generated list
- **Missing videos**: Logs warnings, continues with available videos
- **Network errors**: For URLs, shows error in console

### Debug Information:
Check browser console for:
- `Loading videos from JSON config`
- `Video loading errors`
- `Using JSON config: true/false`

## 🔧 Utility Functions

### Available Functions:
```typescript
// Load videos from JSON config
loadVideoConfiguration()

// Get titles from config
getVideoTitlesFromConfig()

// Reorder videos
reorderVideos(videoIds: string[])

// Add new video
addVideoToConfig(newVideo)

// Remove video
removeVideoFromConfig(videoId)

// Toggle enabled state
toggleVideoEnabled(videoId)
```

## 📈 Next Steps

1. **Test JSON config**: Set `useJsonConfig: true`
2. **Customize videos**: Edit `videoConfig.json`
3. **Add external videos**: Use URLs for remote content
4. **Implement UI**: Create interface for editing JSON config
5. **Add validation**: Schema validation for JSON config

## 🎯 Benefits

- **Flexible ordering**: Không cần rename files
- **Mixed sources**: Local + external videos
- **Metadata support**: Rich information per video
- **Easy management**: JSON file dễ edit
- **Backward compatible**: Không break existing setup
